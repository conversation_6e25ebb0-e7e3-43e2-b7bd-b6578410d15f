'use client';

import React, { useState, useEffect } from 'react';
import { db, auth } from '../../lib/firebase/firebaseClient';
import { collection, addDoc, getDocs, query, orderBy, where, onSnapshot } from 'firebase/firestore';
import { useAuthState } from 'react-firebase-hooks/auth';
import CompanyForm from './components/CompanyForm';
import CompanyList from './components/CompanyList';
import Dashboard from './components/Dashboard';
import InteractionForm from './components/InteractionForm';
import FollowUpManager from './components/FollowUpManager';
import AIProspectImport from './components/AIProspectImport';

export default function CRMPage() {
  const [user, loading, error] = useAuthState(auth);
  const [activeTab, setActiveTab] = useState('dashboard');
  const [companies, setCompanies] = useState([]);
  const [selectedCompany, setSelectedCompany] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showCompanyForm, setShowCompanyForm] = useState(false);
  const [showInteractionForm, setShowInteractionForm] = useState(false);
  const [showAIImport, setShowAIImport] = useState(false);

  // Real-time listener for companies
  useEffect(() => {
    if (!user) return;
    
    const companiesRef = collection(db, 'companies');
    const q = query(
      companiesRef, 
      where('created_by', '==', user.uid),
      orderBy('created_date', 'desc')
    );
    
    const unsubscribe = onSnapshot(q, (snapshot) => {
      const companiesData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      setCompanies(companiesData);
      setIsLoading(false);
    });

    return () => unsubscribe();
  }, [user]);

  const handleAddCompany = () => {
    setSelectedCompany(null);
    setShowCompanyForm(true);
  };

  const handleEditCompany = (company) => {
    setSelectedCompany(company);
    setShowCompanyForm(true);
  };

  const handleAddInteraction = (company) => {
    setSelectedCompany(company);
    setShowInteractionForm(true);
  };

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;
  if (!user) return <div>Please log in to access the CRM.</div>;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <h1 className="text-2xl font-bold text-gray-900">Deal Sourcing CRM</h1>
            <div className="flex space-x-4">
              <button
                onClick={() => setShowAIImport(true)}
                className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors"
              >
                🤖 AI Import
              </button>
              <button
                onClick={handleAddCompany}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Add Prospect
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex space-x-8">
            {[
              { id: 'dashboard', label: 'Dashboard' },
              { id: 'prospects', label: 'Prospects' },
              { id: 'followups', label: 'Follow-ups' },
              { id: 'reports', label: 'Reports' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <>
            {activeTab === 'dashboard' && <Dashboard companies={companies} />}
            {activeTab === 'prospects' && (
              <CompanyList 
                companies={companies}
                onEdit={handleEditCompany}
                onAddInteraction={handleAddInteraction}
              />
            )}
            {activeTab === 'followups' && <FollowUpManager companies={companies} />}
            {activeTab === 'reports' && <div>Reports coming soon...</div>}
          </>
        )}
      </div>

      {/* Modals */}
      {showCompanyForm && (
        <CompanyForm
          company={selectedCompany}
          onClose={() => setShowCompanyForm(false)}
          onSave={() => {
            setShowCompanyForm(false);
            setSelectedCompany(null);
          }}
        />
      )}

      {showInteractionForm && selectedCompany && (
        <InteractionForm
          company={selectedCompany}
          onClose={() => setShowInteractionForm(false)}
          onSave={() => {
            setShowInteractionForm(false);
            setSelectedCompany(null);
          }}
        />
      )}

      {showAIImport && (
        <AIProspectImport
          onClose={() => setShowAIImport(false)}
          onImportComplete={() => {
            // Data will automatically update via real-time listener
          }}
        />
      )}
    </div>
  );
}

