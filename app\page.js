'use client';

import React, { useState, useRef } from 'react';
import Link from 'next/link';
import BGPattern from '../components/ui/BGPattern';

export default function Home() {
  const [activeSection, setActiveSection] = useState('dashboard');

  return (
    <div className="min-h-screen bg-[#f5f5f5] text-black font-sans tracking-tighter">
      {/* Hero Section with Background Pattern */}
      <section className="w-full overflow-hidden relative py-8 md:py-16">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-gray-100 opacity-50" />
        <BGPattern 
          variant="dots" 
          mask="fade-edges" 
          size={20} 
          fill="rgba(0,0,0,0.08)" 
        />
        <div className="relative z-20 max-w-7xl mx-auto px-4 md:px-6">
          <div className="text-center mb-8">
            <div className="relative backdrop-blur-xl bg-white/20 rounded-xl border border-white/30 shadow-2xl p-6 md:p-12 backdrop-saturate-150 max-w-4xl mx-auto">
              <div className="absolute -top-px -left-px -right-px h-px bg-gradient-to-r from-transparent via-white/60 to-transparent" />
              <div className="absolute -bottom-px -left-px -right-px h-px bg-gradient-to-r from-transparent via-white/40 to-transparent" />

              <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold leading-[0.9] mb-4 font-sans tracking-tighter text-black">
                Inside Strategy Dashboard
              </h1>
              <p className="text-base md:text-lg mb-6 font-light text-black/80 max-w-2xl mx-auto">
                Your complete platform for managing client relationships, deal flow, and business transactions from initial contact to successful closing.
              </p>
              <div className="flex flex-wrap gap-3 justify-center items-center">
                <Link href="/sms" className="gradient-button text-black font-bold py-3 px-6 text-base rounded-lg">
                  Client Messages
                </Link>
                <Link href="/crm" className="gradient-button text-black font-bold py-3 px-6 text-base rounded-lg">
                  Deal Sourcing CRM
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
