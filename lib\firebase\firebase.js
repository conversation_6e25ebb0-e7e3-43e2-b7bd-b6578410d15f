import { initializeApp } from "firebase/app";
import { getAuth } from "firebase/auth";
import { getFirestore } from "firebase/firestore";

const firebaseConfig = {
    apiKey: "AIzaSyDUry6QPaonpP0VzpB9YYXQy4G-PvrOZy0",
    authDomain: "inside-strategy.firebaseapp.com",
    databaseURL: "https://inside-strategy-default-rtdb.firebaseio.com",
    projectId: "inside-strategy",
    storageBucket: "inside-strategy.firebasestorage.app",
    messagingSenderId: "920642635898",
    appId: "1:920642635898:web:32f2bd5cb4abb62d5913c3",
    measurementId: "G-3Q5P57V3K0"
};

const app = initializeApp(firebaseConfig);
export const auth = getAuth(app);
export const db = getFirestore(app); // Add this line
