'use client';

import Link from 'next/link';
import { useState, useEffect, Fragment } from 'react';
import { signInWithGoogle, firebaseSignOut } from '../auth';
import { useAuthState } from 'react-firebase-hooks/auth';
import { useDocument } from 'react-firebase-hooks/firestore';
import { auth, db } from '../lib/firebase/firebaseClient';
import { doc, setDoc } from 'firebase/firestore';
import { useRouter } from 'next/navigation';
import { Menu, Transition } from '@headlessui/react';
import { Menu as MenuIcon, X } from 'lucide-react';
import NextImage from 'next/image';
import FirstTimeLoginModal from './FirstTimeLoginModal';
import cx from 'classnames';

const AuthButton = ({ children, onClick }) => (
  <button
    onClick={onClick}
    className="px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:opacity-90 transition-all duration-200 shadow-md backdrop-blur-sm"
  >
    {children}
  </button>
);

const Header = () => {
  const [user, loading, error] = useAuthState(auth);
  const [userDoc] = useDocument(user ? doc(db, 'users', user.uid) : null);
  const isAdmin = userDoc?.data()?.isAdmin;
  const [showModal, setShowModal] = useState(false);
  const [menuOpen, setMenuOpen] = useState(false);
  const router = useRouter();

  const handleLogout = async () => {
    await firebaseSignOut();
    setMenuOpen(false);
    router.push('/');
  };

  const handleFirstTimeLogin = async (formData) => {
    const userRef = doc(db, 'users', user.uid);
    const branchRequested = formData.branch?.endsWith('-requested')
      ? formData.branch
      : `${formData.branch || 'lwr'}-requested`;

    await setDoc(
      userRef,
      {
        address: formData.address || '',
        branch: branchRequested,
        wage: formData.wage || 16,
        userId: user.uid,
        displayName: user.displayName || user.email.split('@')[0],
        email: user.email,
        isAdmin: false,
        firstTimeLogin: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        paymentType: 'hourly',
        commissionRate: 0.27,
      },
      { merge: true }
    );

    setShowModal(false);
  };

  useEffect(() => {
    if (user && userDoc?.data()?.firstTimeLogin) {
      setShowModal(true);
    }
  }, [user, userDoc]);

  const navItems = [
    { href: '/', label: 'Dashboard' },
    { href: '/sms', label: 'Messages' },
    { href: '/crm', label: 'CRM' },
    { href: '/booking', label: 'Booking' }
  ];

  return (
    <>
      <nav
        className={cx(
          "fixed top-0 left-0 w-full z-50 transition-all duration-300",
          "backdrop-blur-xl bg-white/20 border border-blue-200/30 shadow-xl backdrop-saturate-150"
        )}
      >
        {/* Glass effect decorative elements */}
        <div className="absolute -top-px -left-px -right-px h-px bg-gradient-to-r from-transparent via-blue-200/60 to-transparent" />
        <div className="absolute -bottom-px -left-px -right-px h-px bg-gradient-to-r from-transparent via-blue-200/40 to-transparent" />

        <div className="w-full h-20 flex items-center justify-between px-4 md:px-6 lg:px-8 relative">
          {/* Logo */}
          <div className="flex items-center gap-2">
            <Link href="/" className="text-3xl font-bold font-sans tracking-tighter text-black hover:text-blue-700 transition-colors" onClick={() => setMenuOpen(false)}>
              Inside Strategy
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center gap-6">
            {user && (
              <>
                {navItems.map((item) => (
                  <Link
                    key={item.href}
                    href={item.href}
                    className="text-black hover:text-blue-700 transition-colors font-medium"
                  >
                    {item.label}
                  </Link>
                ))}
                <button
                  onClick={handleLogout}
                  className="text-black hover:text-blue-700 transition-colors font-medium"
                >
                  Sign out
                </button>
              </>
            )}
            {!user && (
              <AuthButton onClick={() => signInWithGoogle(setShowModal)}>
                Login
              </AuthButton>
            )}
          </div>

          {/* Mobile Menu Button */}
          <button
            className="md:hidden text-black hover:text-blue-700 relative h-8 w-8 flex items-center justify-center transition-colors"
            onClick={() => setMenuOpen(!menuOpen)}
            aria-label="Toggle menu"
          >
            <MenuIcon
              className={cx(
                'absolute h-8 w-8 transition-all duration-300',
                { 'opacity-0 rotate-90': menuOpen },
                { 'opacity-100 rotate-0': !menuOpen }
              )}
            />
            <X
              className={cx(
                'absolute h-8 w-8 transition-all duration-300',
                { 'opacity-100 rotate-0': menuOpen },
                { 'opacity-0 -rotate-90': !menuOpen }
              )}
            />
          </button>
        </div>
      </nav>

      {/* Mobile Menu */}
      <div
        className={cx(
          "fixed inset-0 w-full backdrop-blur-xl bg-white/25 border border-blue-200/30 shadow-3xl backdrop-saturate-150 text-black z-40 flex flex-col pt-20 transition-all duration-300 ease-in-out overflow-y-auto md:hidden",
          { 'opacity-100 pointer-events-auto': menuOpen },
          { 'opacity-0 pointer-events-none': !menuOpen }
        )}
      >
        {/* Glass effect decorative elements for mobile menu */}
        <div className="absolute -top-px -left-px -right-px h-px bg-gradient-to-r from-transparent via-blue-200/60 to-transparent" />
        <div className="absolute -bottom-px -left-px -right-px h-px bg-gradient-to-r from-transparent via-blue-200/40 to-transparent" />

        <div className="container mx-auto py-6 px-5 flex-1 flex flex-col relative">
          <div className="flex flex-col space-y-6 mt-8 pb-20">
            <Link
              href="/"
              className="text-3xl font-bold py-3 border-b border-blue-200/40 font-sans tracking-tighter text-black hover:text-blue-700 transition-colors"
              onClick={() => setMenuOpen(false)}
            >
              Home
            </Link>

            {user ? (
              <>
                {navItems.map((item) => (
                  <Link
                    key={item.href}
                    href={item.href}
                    className="text-3xl font-bold py-3 border-b border-blue-200/40 font-sans tracking-tighter text-black hover:text-blue-700 transition-colors"
                    onClick={() => setMenuOpen(false)}
                  >
                    {item.label}
                  </Link>
                ))}
                <button
                  onClick={handleLogout}
                  className="text-3xl font-bold py-3 border-b border-blue-200/40 font-sans tracking-tighter text-black hover:text-blue-700 transition-colors text-left"
                >
                  Sign out
                </button>
              </>
            ) : (
              <button
                onClick={() => {
                  signInWithGoogle(setShowModal);
                  setMenuOpen(false);
                }}
                className="text-3xl font-bold py-3 border-b border-blue-200/40 font-sans tracking-tighter text-black hover:text-blue-700 transition-colors text-left"
              >
                Login
              </button>
            )}
          </div>
        </div>
      </div>

      {showModal && user && (
        <FirstTimeLoginModal
          user={user}
          onClose={() => setShowModal(false)}
          onSubmit={handleFirstTimeLogin}
        />
      )}
    </>
  );
};

export default Header;
