import { GoogleAuthProvider, signInWithPopup, signOut, onAuthStateChanged } from 'firebase/auth';
import { doc, getDoc, setDoc } from 'firebase/firestore';
import { db, auth } from './lib/firebase/firebaseClient';

export const signInWithGoogle = async (setShowModal) => {
  const provider = new GoogleAuthProvider();
  provider.setCustomParameters({
    prompt: "select_account",
  });

  try {
    const result = await signInWithPopup(auth, provider);
    const user = result.user;

    // Check if user document exists in Firestore
    const userRef = doc(db, 'users', user.uid);
    const userDoc = await getDoc(userRef);

    if (!userDoc.exists()) {
      // Trigger modal for first-time login
      setShowModal(true);
    }

    return user;
  } catch (error) {
    console.error('Google Sign-In Error:', error);
    throw error;
  }
};


export const firebaseSignOut = () => {
  const auth = getAuth();
  return signOut(auth);
};

export const onAuthStateChangedListener = (callback) => {
  const auth = getAuth();
  return onAuthStateChanged(auth, (user) => {
    console.log('Auth state changed:', user ? `User ${user.email} is signed in` : 'User signed out');
    callback(user);
  });
};

export const getUser = () => {
  const auth = getAuth();
  return auth.currentUser;
};

export { auth };
