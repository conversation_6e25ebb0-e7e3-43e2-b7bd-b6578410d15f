'use client';

import React, { useState, useRef } from 'react';

export default function Footer() {
  const footerRef = useRef(null);

  return (
    <footer ref={footerRef} className="bg-black text-white py-12 md:py-16" aria-labelledby="footer-title">
      <div className="max-w-7xl mx-auto px-4 md:px-6 transition-all duration-700">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-8">

          {/* Company Info */}
          <div className="lg:col-span-2">
            <h3 id="footer-title" className="text-2xl md:text-3xl font-bold mb-4 font-sans">
              <span className="text-white">Inside Strategy</span>
            </h3>
            <p className="text-gray-300 mb-4 max-w-md">
              You built a great business. When you're ready to sell, we'll help you get the right price from the right buyer while you stay in complete control of the process.
            </p>
            <div className="space-y-2">
              <p className="text-gray-300">
                <span className="font-semibold">Phone:</span>
                <a href="tel:************" className="hover:text-white transition-colors ml-2">
                  (*************
                </a>
              </p>
              <p className="text-gray-300">
                <span className="font-semibold">Email:</span>
                <a href="mailto:<EMAIL>" className="hover:text-white transition-colors ml-2">
                  <EMAIL>
                </a>
              </p>
            </div>

            {/* Social Media Links */}
            <div className="mt-4">
              <h4 className="text-sm font-semibold text-gray-300 mb-2">Connect With Us</h4>
              <div className="flex space-x-4">
                <a
                  href="https://www.facebook.com/insidestrategy.co/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-300 hover:text-white transition-colors"
                  aria-label="Facebook"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z" />
                  </svg>
                </a>
                <a
                  href="https://www.linkedin.com/in/levi-taylor-64735a281/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-300 hover:text-white transition-colors"
                  aria-label="LinkedIn"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" />
                  </svg>
                </a>
                <a
                  href="https://www.youtube.com/@InsideStrategy"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-300 hover:text-white transition-colors"
                  aria-label="YouTube"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z" />
                  </svg>
                </a>
              </div>
            </div>
          </div>

          {/* Service Areas */}
          <div>
            <h4 className="text-lg font-bold mb-4 font-sans">Service Areas</h4>
            <ul className="space-y-2 text-gray-300">
              <li>
                <a href="/locations/lawrence" className="hover:text-white transition-colors">
                  Lawrence, KS
                </a>
              </li>
              <li>
                <a href="/locations/kansas-city" className="hover:text-white transition-colors">
                  Kansas City
                </a>
              </li>
              <li>
                <a href="/locations/overland-park" className="hover:text-white transition-colors">
                  Overland Park
                </a>
              </li>
              <li>
                <a href="#service-areas" className="hover:text-white transition-colors">
                  Johnson County
                </a>
              </li>
            </ul>
          </div>

          {/* Services */}
          <div>
            <h4 className="text-lg font-bold mb-4 font-sans">Our Services</h4>
            <ul className="space-y-2 text-gray-300">
              <li>
                <a href="/business-valuation" className="hover:text-white transition-colors">
                  Business Valuation
                </a>
              </li>
              <li>
                <a href="/our-process" className="hover:text-white transition-colors">
                  Exit Planning
                </a>
              </li>
              <li>
                <a href="/sell-my-business" className="hover:text-white transition-colors">
                  Business Preparation
                </a>
              </li>
              <li>
                <a href="/contact" className="hover:text-white transition-colors">
                  Buyer Matching
                </a>
              </li>
              <li>
                <a href="/deal-financing" className="hover:text-white transition-colors">
                  Deal Financing
                </a>
              </li>
              <li>
                <a href="/contact" className="hover:text-white transition-colors">
                  Transaction Management
                </a>
              </li>
            </ul>
          </div>
        </div>

        {/* Professional Credentials */}
        <div className="border-t border-gray-700 pt-8 mb-8">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="mb-4 md:mb-0">
              <p className="text-gray-300 text-sm">
                Independent Business Broker • Confidential Process • Local Knowledge
              </p>
            </div>
          </div>
        </div>

        {/* Bottom Footer */}
        <div className="border-t border-gray-700 pt-8">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="mb-4 md:mb-0">
              <p className="text-gray-400 text-sm">
                © {new Date().getFullYear()} Inside Strategy. All rights reserved.
              </p>
            </div>
            <div className="flex space-x-6 text-sm">
              <a href="/privacy" className="text-gray-400 hover:text-white transition-colors">
                Privacy Policy
              </a>
              <a href="/contact" className="text-gray-400 hover:text-white transition-colors">
                Contact
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}