'use client';

import React, { useState, useEffect } from 'react';
import { db, auth } from '../../../lib/firebase/firebaseClient';
import { collection, query, orderBy, onSnapshot, updateDoc, doc, where } from 'firebase/firestore';
import { useAuthState } from 'react-firebase-hooks/auth';

export default function FollowUpManager({ companies }) {
  const [followUps, setFollowUps] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('pending'); // pending, overdue, completed, all

  useEffect(() => {
    const followUpsRef = collection(db, 'followups');
    const q = query(followUpsRef, orderBy('scheduled_date', 'asc'));
    
    const unsubscribe = onSnapshot(q, (snapshot) => {
      const followUpsData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      setFollowUps(followUpsData);
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  const getFilteredFollowUps = () => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    return followUps.filter(followUp => {
      const scheduledDate = new Date(followUp.scheduled_date);
      const isOverdue = scheduledDate < today && !followUp.completed;
      const isPending = !followUp.completed;
      const isCompleted = followUp.completed;

      switch (filter) {
        case 'overdue':
          return isOverdue;
        case 'pending':
          return isPending && !isOverdue;
        case 'completed':
          return isCompleted;
        case 'all':
        default:
          return true;
      }
    });
  };

  const markAsCompleted = async (followUpId, resultNotes = '') => {
    try {
      await updateDoc(doc(db, 'followups', followUpId), {
        completed: true,
        completed_date: new Date().toISOString(),
        result_notes: resultNotes
      });
    } catch (error) {
      console.error('Error marking follow-up as completed:', error);
      alert('Error updating follow-up. Please try again.');
    }
  };

  const filteredFollowUps = getFilteredFollowUps();
  const overdueCount = followUps.filter(f => {
    const scheduledDate = new Date(f.scheduled_date);
    const today = new Date();
    return scheduledDate < today && !f.completed;
  }).length;

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-sm font-medium text-gray-600">Total Follow-ups</h3>
          <p className="text-2xl font-bold text-blue-600">{followUps.length}</p>
        </div>
        
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-sm font-medium text-gray-600">Pending</h3>
          <p className="text-2xl font-bold text-yellow-600">
            {followUps.filter(f => !f.completed).length}
          </p>
        </div>
        
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-sm font-medium text-gray-600">Overdue</h3>
          <p className="text-2xl font-bold text-red-600">{overdueCount}</p>
        </div>
        
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-sm font-medium text-gray-600">Completed</h3>
          <p className="text-2xl font-bold text-green-600">
            {followUps.filter(f => f.completed).length}
          </p>
        </div>
      </div>

      {/* Filter Tabs */}
      <div className="bg-white rounded-lg shadow">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {[
              { id: 'pending', label: 'Pending', count: followUps.filter(f => !f.completed && new Date(f.scheduled_date) >= new Date()).length },
              { id: 'overdue', label: 'Overdue', count: overdueCount },
              { id: 'completed', label: 'Completed', count: followUps.filter(f => f.completed).length },
              { id: 'all', label: 'All', count: followUps.length }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setFilter(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  filter === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label} ({tab.count})
              </button>
            ))}
          </nav>
        </div>

        {/* Follow-ups List */}
        <div className="p-6">
          {filteredFollowUps.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No follow-ups found for the selected filter.
            </div>
          ) : (
            <div className="space-y-4">
              {filteredFollowUps.map((followUp) => {
                const scheduledDate = new Date(followUp.scheduled_date);
                const isOverdue = scheduledDate < new Date() && !followUp.completed;
                const isToday = scheduledDate.toDateString() === new Date().toDateString();

                return (
                  <div
                    key={followUp.id}
                    className={`p-4 rounded-lg border ${
                      isOverdue ? 'border-red-200 bg-red-50' :
                      isToday ? 'border-yellow-200 bg-yellow-50' :
                      followUp.completed ? 'border-green-200 bg-green-50' :
                      'border-gray-200 bg-white'
                    }`}
                  >
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <h4 className="font-medium text-gray-900">{followUp.company_name}</h4>
                          <span className={`px-2 py-1 text-xs rounded-full ${
                            followUp.priority === 'High' ? 'bg-red-100 text-red-800' :
                            followUp.priority === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {followUp.priority}
                          </span>
                          {isOverdue && (
                            <span className="px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full">
                              Overdue
                            </span>
                          )}
                          {isToday && (
                            <span className="px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full">
                              Due Today
                            </span>
                          )}
                        </div>
                        
                        <p className="text-sm text-gray-600 mt-1">{followUp.description}</p>
                        
                        <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                          <span>Type: {followUp.followup_type}</span>
                          <span>Due: {scheduledDate.toLocaleDateString()}</span>
                          {followUp.completed && followUp.completed_date && (
                            <span>Completed: {new Date(followUp.completed_date).toLocaleDateString()}</span>
                          )}
                        </div>

                        {followUp.result_notes && (
                          <div className="mt-2 p-2 bg-gray-100 rounded text-sm">
                            <strong>Result:</strong> {followUp.result_notes}
                          </div>
                        )}
                      </div>

                      <div className="flex space-x-2">
                        {!followUp.completed && (
                          <button
                            onClick={() => {
                              const notes = prompt('Add completion notes (optional):');
                              markAsCompleted(followUp.id, notes || '');
                            }}
                            className="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700"
                          >
                            Mark Complete
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
