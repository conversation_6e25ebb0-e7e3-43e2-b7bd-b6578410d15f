'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import PropTypes from 'prop-types';
import { onAuthStateChangedListener } from '../../auth.js'; // Adjust path as needed
import { db } from '../../lib/firebase/firebase'; // Adjust path as needed
import { doc, getDoc, setDoc, updateDoc, collection, onSnapshot } from 'firebase/firestore';

export default function MessengerPopup({ isOpen, onClose, businessNumber, clientNumber, location }) {
  const [clientInfo, setClientInfo] = useState({
    firstName: '',
    lastName: '',
    phone: ''
  });
  const [isEditingName, setIsEditingName] = useState(false);
  const [tempFirstName, setTempFirstName] = useState('');
  const [tempLastName, setTempLastName] = useState('');

  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isFetching, setIsFetching] = useState(false);
  const [delay, setDelay] = useState(0);
  const [userFirstName, setUserFirstName] = useState('');
  const [userFullName, setUserFullName] = useState('');
  const [isGeneratingResponse, setIsGeneratingResponse] = useState(false);
  const [currentPhraseIndex, setCurrentPhraseIndex] = useState(0);
  const phrases = [
    "Formulating a response...",
    "Getting information",
    "Reading conversation",
    "Analyzing context",
    "Crafting reply"
  ];
  const messagesEndRef = useRef(null);
  const MAX_ROWS = 6;
  const ROW_HEIGHT = 24;             // adjust to match your line-height
  const MAX_HEIGHT = MAX_ROWS * ROW_HEIGHT;
  const textareaRef = useRef(null);


  useEffect(() => {
    let unsub;

    if (isOpen) {
      const collectionName = `sms-${location.toLowerCase()}`;
      const clientDocRef = doc(db, collectionName, clientNumber);

      // Set up real-time listener
      unsub = onSnapshot(clientDocRef, (doc) => {
        if (doc.exists()) {
          const clientData = doc.data();
          const rawMessages = clientData.messages || [];

          const processedMessages = rawMessages.map((msg) => {
            const ts = msg.timestamp;
            const date = new Date(
              (ts?.seconds || ts?._seconds || 0) * 1000 +
              (ts?.nanoseconds || ts?._nanoseconds || 0) / 1e6
            );

            return {
              ...msg,
              direction: msg.direction || 'incoming',
              timestamp: date.toLocaleString('en-US', {
                month: 'short',
                day: 'numeric',
                hour: 'numeric',
                minute: '2-digit',
                hour12: true
              }),
              rawTimestamp: date.getTime(),
              message: msg.message || '',
            };
          }).sort((a, b) => a.rawTimestamp - b.rawTimestamp);

          setMessages(processedMessages);
          setClientInfo({
            firstName: clientData.firstName?.trim() || '',
            lastName: clientData.lastName?.trim() || '',
            phone: clientNumber
          });
        }
      });
    }

    return () => {
      if (unsub) unsub();
    };
  }, [isOpen, businessNumber, clientNumber, location]); // Add proper dependencies

  // Remove the old setupRealtimeUpdates and fetchConversation functions
  // Replace with this simplified version
  const fetchInitialData = useCallback(async () => {
    if (!isOpen) return;

    try {
      const collectionName = `sms-${location.toLowerCase()}`;
      const clientDocRef = doc(db, collectionName, clientNumber);
      const clientSnap = await getDoc(clientDocRef);

      if (clientSnap.exists()) {
        const clientData = clientSnap.data();
        setClientInfo({
          firstName: clientData.firstName?.trim() || '',
          lastName: clientData.lastName?.trim() || '',
          phone: clientNumber
        });
      }
    } catch (error) {
      console.error('Error fetching initial data:', error);
    }
  }, [isOpen, businessNumber, clientNumber, location]);

  // Update the useEffect that triggers on open
  useEffect(() => {
    if (isOpen) {
      fetchInitialData();
    }
  }, [isOpen, fetchInitialData]);



  useEffect(() => {
    const el = textareaRef.current;
    if (!el) return;
    el.style.height = 'auto';
    const newH = Math.min(el.scrollHeight, MAX_HEIGHT);
    el.style.height = `${newH}px`;
    el.style.overflowY = el.scrollHeight > MAX_HEIGHT ? 'auto' : 'hidden';
  }, [newMessage]);
  const formatPhoneNumber = (phone) => {
    if (!phone) return '';
    const cleaned = phone.replace(/\D/g, '');
    const match = cleaned.match(/(\d{4})$/);
    return match ? `•••• ${match[1]}` : '••••';
  };

  // Fetch authenticated user's name
  useEffect(() => {
    const unsubscribe = onAuthStateChangedListener((user) => {
      if (user?.displayName) {
        const [firstName] = user.displayName.split(' ');
        setUserFirstName(firstName);
        setUserFullName(user.displayName);
      } else {
        setUserFirstName('');
        setUserFullName('');
      }
    });
    return () => unsubscribe();
  }, []);

  const generateAutoResponse = useCallback(async () => {
    setIsGeneratingResponse(true);
    setIsLoading(true);
    try {
      const requestData = {
        businessNumber,
        clientNumber,
        messages: messages.slice(-10),
        userName: userFirstName
      };
      if (newMessage.trim()) {
        requestData.customPrompt = newMessage.trim();
      }

      const response = await fetch('/api/generate-response', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        throw new Error(`Failed to generate response: ${response.status}`);
      }

      const data = await response.json();
      console.log('🏷  [generateAutoResponse] raw API payload:', data);
      setNewMessage(data.response);
    } catch (error) {
      console.error('Error generating auto response:', error);
      alert('Failed to generate auto response. Please try again.');
    } finally {
      setIsLoading(false);
      setIsGeneratingResponse(false);
    }
  }, [businessNumber, clientNumber, messages, userFirstName, newMessage]);












  // Fetch conversation history
  const fetchConversation = useCallback(async () => {
    if (!businessNumber || !clientNumber || !location) return;

    try {
      const collectionName = `sms-${location.toLowerCase()}`;
      const clientDocRef = doc(db, collectionName, clientNumber);
      const clientSnap = await getDoc(clientDocRef);

      if (clientSnap.exists()) {
        const clientData = clientSnap.data();
        setClientInfo({
          firstName: clientData.firstName?.trim() || '',
          lastName: clientData.lastName?.trim() || '',
          phone: clientNumber
        });
      }
    } catch (error) {
      console.error('Error fetching initial conversation:', error);
    }
  }, [businessNumber, clientNumber, location]);



  const handleNameUpdate = async () => {
    try {
      if (!tempFirstName.trim() && !tempLastName.trim()) {
        alert('Please enter at least one name field');
        return;
      }

      const collectionName = `sms-${location.toLowerCase()}`;
      const clientDocRef = doc(db, collectionName, clientNumber);

      // Use setDoc with merge: true to update only specific fields
      await setDoc(clientDocRef, {
        firstName: tempFirstName.trim(),
        lastName: tempLastName.trim()
      }, { merge: true }); // This preserves existing data in the document

      setClientInfo(prev => ({
        ...prev,
        firstName: tempFirstName.trim(),
        lastName: tempLastName.trim()
      }));
      setIsEditingName(false);

    } catch (error) {
      console.error('Error updating name:', error);
      alert('Failed to update client name');
    }
  };




  useEffect(() => {
    if (isOpen) fetchConversation();
  }, [isOpen, fetchConversation]);

  // Scroll to latest message
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Cycle through phrases for overlay
  useEffect(() => {
    if (isGeneratingResponse) {
      const interval = setInterval(() => {
        setCurrentPhraseIndex((prev) => (prev + 1) % phrases.length);
      }, 2000); // Change every 2 seconds
      return () => clearInterval(interval);
    }
  }, [isGeneratingResponse, phrases.length]);

  // Fetch availability
  const fetchAvailability = useCallback(async () => {
    try {
      const response = await fetch(
        `https://us-central1-detail-on-the-go-universal.cloudfunctions.net/phone-availability?to=${encodeURIComponent(businessNumber)}&from=${encodeURIComponent(clientNumber)}`
      );
      if (!response.ok) throw new Error('Failed to fetch availability');
      return await response.json();
    } catch (error) {
      console.error('Error fetching availability:', error);
      return [];
    }
  }, [businessNumber, clientNumber]);

  // Send message
  const handleSendMessage = useCallback(async () => {
    if (!newMessage.trim()) return;
    setIsLoading(true);

    const signature = userFirstName ? `-${userFirstName}, Detail On The Go` : '-Detail On The Go';
    const messageWithSignature = `${newMessage.trim()} ${signature}`;

    try {
      // 1. Send the message via cloud function
      const smsResponse = await fetch(
        `https://us-central1-detail-on-the-go-universal.cloudfunctions.net/sms?to=${encodeURIComponent(clientNumber)}&from=${encodeURIComponent(businessNumber)}&message=${encodeURIComponent(messageWithSignature)}&delay=${delay}`,
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
        }
      );

      if (!smsResponse.ok) throw new Error('Failed to send message');

      // 2. Clear input field
      setNewMessage('');

      // 3. Send to Google Sheets if needed
      const now = new Date();
      const clientData = {
        timestamp: now.toLocaleString(),
        branch: location,
        businessNumber,
        name: `${clientInfo.firstName} ${clientInfo.lastName}`.trim() || 'Unknown Client',
        phone: clientNumber,
        message: messageWithSignature,
      };

      if (/(I've just arrived|I'm headed your|wrapping|finished)/i.test(newMessage)) {
        const sheetsResponse = await fetch(
          'https://us-central1-detail-on-the-go-universal.cloudfunctions.net/detail-sms-timing',
          {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(clientData),
          }
        );
        if (!sheetsResponse.ok) console.error('Failed to send client data to backend');
        else console.log('Client data successfully sent to backend');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      // Optionally restore message if send failed
      // setNewMessage(messageWithSignature);
    } finally {
      setIsLoading(false);
    }
  }, [newMessage, userFirstName, businessNumber, clientNumber, delay, location, clientInfo]);

  // Initiate call
  const initiateCall = useCallback(() => {
    fetch(
      `https://us-central1-detail-on-the-go-universal.cloudfunctions.net/LWR-create-call?businessnumber=${encodeURIComponent(businessNumber)}&forwardnumber=${encodeURIComponent(clientNumber)}&clientnumber=${encodeURIComponent(clientNumber)}`,
      { method: 'POST' }
    )
      .then((res) => {
        if (!res.ok) throw new Error('Failed to initiate call');
        return res.json();
      })
      .then(() => alert('Call initiated successfully!'))
      .catch((error) => {
        console.error('Error initiating call:', error);
        alert('Failed to initiate call.');
      });
  }, [businessNumber, clientNumber]);

  // Apply shortcut messages
  const applyShortcut = useCallback(async (shortcut) => {
    const messagesMap = {
      onMyWay: "I'm headed your way now.🚗",
      arrived: "I've just arrived, and will start shortly.🚗🚐",
      delay: "My current detail is taking a bit longer than expected, so I may be running late to our appointment today. I apologize for the delay and will update you as soon as I'm on my way.",
      early: "I've just finished my current detail earlier than expected, and can head your way now if that works with your schedule. Let me know if not - thanks!",
      finished1: "I'm close to wrapping up your detail now! If it's convenient, would you like to come look?",
      finished2: "I've just finished your detail. Thank you very much for having me out today, I'll see you next time!😊",
      LWRReview: "Thank you so much for having me out! If you enjoyed your detail experience, here's an easy link to leave a review😊 https://g.page/r/CS98X9jMS0IREBM/review",
    };
    if (shortcut === 'getAvailability') {
      setIsLoading(true);
      const availableSlots = await fetchAvailability();
      setIsLoading(false);
      setNewMessage(
        availableSlots.length > 0
          ? `Our next availability is:\n${availableSlots.slice(0, 5).join('\n')}. Do any of these times work with your schedule?`
          : 'I don’t have any availability in the next 60 days.'
      );
    } else {
      setNewMessage(messagesMap[shortcut] || '');
    }
  }, [fetchAvailability]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/30 backdrop-blur-sm flex justify-center items-center z-50 p-4">
      <div
        className="backdrop-blur-2xl bg-white/20 border border-white/30 rounded-lg shadow-2xl w-full max-w-md flex flex-col overflow-hidden relative"
        style={{ 
          maxHeight: 'calc(100vh - 2rem)',
          height: 'auto'
        }}
      >
        {isGeneratingResponse && (
          <div className="absolute inset-0 z-10 flex items-center justify-center bg-gradient-to-r from-orange-400/90 via-orange-500/90 to-orange-600/90 backdrop-blur-xl rounded-lg">
            <p className="text-white text-lg font-semibold">{phrases[currentPhraseIndex]}</p>
          </div>
        )}
        {isFetching ? (
          <div className="flex justify-center items-center h-full p-4">
            <div className="backdrop-blur-xl bg-white/30 rounded-lg p-6 border border-white/40">
              <p className="text-gray-700">Loading conversation...</p>
            </div>
          </div>
        ) : (
          <>
            <header className="flex justify-between items-center p-4 border-b border-white/20 backdrop-blur-xl bg-white/10 flex-shrink-0">
              <div className="flex items-center gap-3">
                <div className="flex flex-col gap-1">
                  {isEditingName ? (
                    <div className="flex gap-2">
                      <input
                        value={tempFirstName}
                        onChange={(e) => setTempFirstName(e.target.value)}
                        placeholder="First name"
                        className="w-20 px-2 py-1 border border-white/30 rounded-md backdrop-blur-sm bg-white/50 text-gray-800 placeholder-gray-500 text-sm"
                      />
                      <input
                        value={tempLastName}
                        onChange={(e) => setTempLastName(e.target.value)}
                        placeholder="Last name"
                        className="w-20 px-2 py-1 border border-white/30 rounded-md backdrop-blur-sm bg-white/50 text-gray-800 placeholder-gray-500 text-sm"
                      />
                    </div>
                  ) : (
                    <h2 className="text-lg font-semibold text-gray-800">
                      {clientInfo.firstName || clientInfo.lastName
                        ? `${clientInfo.firstName} ${clientInfo.lastName}`.trim()
                        : 'Unknown Client'}
                    </h2>
                  )}
                  <p className="text-sm text-gray-600">{formatPhoneNumber(clientInfo.phone)}</p>
                </div>
              </div>

              <div className="flex items-center gap-2">
                {/* Edit button */}
                <button
                  onClick={() => {
                    if (isEditingName) {
                      handleNameUpdate();
                    } else {
                      setIsEditingName(true);
                      setTempFirstName(clientInfo.firstName);
                      setTempLastName(clientInfo.lastName);
                    }
                  }}
                  className="p-2 rounded-md backdrop-blur-sm bg-white/40 hover:bg-white/60 transition-all duration-200 text-gray-700"
                >
                  {isEditingName ? '✓' : '✏️'}
                </button>

                {/* Mark as Read button */}
                <button
                  onClick={() => {
                    fetch(
                      `https://inside-strategy-read-message-896343340170.europe-west1.run.app?to=${encodeURIComponent(
                        clientNumber
                      )}&from=${encodeURIComponent(businessNumber)}&message=${encodeURIComponent(
                        'Read'
                      )}&delay=0`,
                      {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                      }
                    ).catch((err) => {
                      console.error('Error marking as read:', err);
                    });
                  }}
                  className="px-3 py-2 rounded-md backdrop-blur-sm bg-blue-500/80 text-white hover:bg-blue-600/80 transition-all duration-200 text-sm font-medium"
                >
                  Mark Read
                </button>

                {/* Close button */}
                <button
                  onClick={onClose}
                  className="p-2 rounded-md backdrop-blur-sm bg-white/40 hover:bg-red-500/80 hover:text-white transition-all duration-200 text-gray-700"
                >
                  ✕
                </button>
              </div>
            </header>

            <main className="flex-1 overflow-y-auto p-4 bg-gradient-to-b from-transparent to-white/5 min-h-0">
              {messages.length > 0 ? (
                messages.map((msg, index) => (
                  <div
                    key={index}
                    className={`mb-4 p-3 rounded-lg shadow-lg max-w-[80%] backdrop-blur-sm border transition-all duration-200 hover:scale-[1.02] ${
                      msg.direction === 'outgoing' 
                        ? 'bg-blue-500/80 text-white ml-auto border-blue-400/30' 
                        : 'bg-white/60 text-gray-800 border-white/40'
                    }`}
                  >
                    <p className="text-sm whitespace-pre-wrap leading-relaxed">{msg.message}</p>
                    <span className={`text-xs block mt-2 ${msg.direction === 'outgoing' ? 'text-blue-100' : 'text-gray-500'}`}>
                      {msg.timestamp}
                    </span>
                  </div>
                ))
              ) : (
                <div className="text-center backdrop-blur-xl bg-white/30 rounded-lg p-6 border border-white/40">
                  <p className="text-gray-600">No messages yet.</p>
                </div>
              )}
              <div ref={messagesEndRef} />
            </main>

            <footer className="p-4 border-t border-white/20 backdrop-blur-xl bg-white/10 flex-shrink-0">
              {/* Quick response buttons */}
              <div className="grid grid-cols-2 gap-2 mb-4">
                <button
                  onClick={generateAutoResponse}
                  disabled={isLoading || isGeneratingResponse}
                  className="px-3 py-2 rounded-md backdrop-blur-sm bg-green-500/80 hover:bg-green-600/80 transition-all duration-200 text-sm font-medium text-white disabled:opacity-50"
                >
                  {isGeneratingResponse ? 'Generating...' : 'Auto Response'}
                </button>
              </div>

              {/* Message input */}
              <textarea
                ref={textareaRef}
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                placeholder="Type a message..."
                className="w-full p-3 border border-white/30 rounded-md resize-none min-h-[3rem] focus:ring-2 focus:ring-blue-400/50 backdrop-blur-sm bg-white/50 text-gray-800 placeholder-gray-500 mb-3"
                style={{ 
                  overflowY: 'hidden',
                  maxHeight: '120px'
                }}
                onInput={(e) => {
                  e.target.style.height = 'auto';
                  const newH = Math.min(e.target.scrollHeight, 120);
                  e.target.style.height = `${newH}px`;
                  e.target.style.overflowY = e.target.scrollHeight > 120 ? 'auto' : 'hidden';
                }}
                aria-label="Message input"
              />

              {/* Send button */}
              <div className="flex gap-2">
                <button
                  onClick={handleSendMessage}
                  disabled={isLoading || isGeneratingResponse}
                  className={`flex-1 px-4 py-3 rounded-md text-white font-medium transition-all duration-200 ${
                    (isLoading || isGeneratingResponse) 
                      ? 'bg-blue-400/60 cursor-not-allowed' 
                      : 'bg-blue-500/80 hover:bg-blue-600/80 shadow-lg'
                  } backdrop-blur-sm`}
                  aria-label="Send message"
                >
                  {isLoading ? 'Sending...' : 'Send'}
                </button>
              </div>
            </footer>
          </>
        )}
      </div>
    </div>
  );
}

MessengerPopup.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  businessNumber: PropTypes.string.isRequired,
  clientNumber: PropTypes.string.isRequired,
  location: PropTypes.string,
};

MessengerPopup.defaultProps = {
  location: '',
};
