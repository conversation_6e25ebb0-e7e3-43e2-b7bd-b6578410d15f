import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY || process.env.NEXT_PUBLIC_OPENAI_API_KEY
});

export async function POST(req) {
  try {
    const { text } = await req.json();

    if (!text?.trim()) {
      return new Response(
        JSON.stringify({ error: 'No text provided' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }

    const systemPrompt = `You are a data extraction expert. Extract prospect/company information from any text format and return a JSON array of companies with their contacts.

REQUIRED OUTPUT FORMAT (JSON only, no other text):
{
  "prospects": [
    {
      "company_name": "string",
      "industry": "string",
      "website": "string", 
      "address": "string",
      "city": "string",
      "state": "string",
      "zip_code": "string",
      "revenue_range": "string",
      "employee_count": "string",
      "description": "string",
      "contacts": [
        {
          "name": "string",
          "title": "string", 
          "email": "string",
          "phone": "string",
          "linkedin": "string"
        }
      ]
    }
  ]
}

EXTRACTION RULES:
- Extract ALL companies mentioned
- For each company, extract ALL contacts/people mentioned
- Clean and standardize phone numbers
- Validate email formats
- Infer industry from context if not explicitly stated
- Extract websites (with or without http/https)
- Parse addresses into components
- Estimate revenue/employee ranges from context
- If multiple contacts for same company, group them together
- Handle various formats: CSV, email signatures, directories, lists, etc.

Extract from this text:`;

    const completion = await openai.chat.completions.create({
      model: 'gpt-4-turbo',
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: text }
      ],
      max_tokens: 4000,
      temperature: 0.1
    });

    const response = completion.choices[0].message.content.trim();
    
    try {
      const parsedData = JSON.parse(response);
      
      // Validate and clean the data
      const cleanedProspects = parsedData.prospects?.map(prospect => ({
        company_name: prospect.company_name || 'Unknown Company',
        industry: prospect.industry || '',
        website: cleanWebsite(prospect.website),
        address: prospect.address || '',
        city: prospect.city || '',
        state: prospect.state || '',
        zip_code: prospect.zip_code || '',
        revenue_range: prospect.revenue_range || '',
        employee_count: prospect.employee_count || '',
        description: prospect.description || '',
        status: 'New Lead',
        priority: 'Medium',
        contacts: prospect.contacts?.map(contact => ({
          name: contact.name || '',
          title: contact.title || '',
          email: cleanEmail(contact.email),
          phone: cleanPhone(contact.phone),
          linkedin: contact.linkedin || '',
          is_primary: false
        })) || []
      })) || [];

      return new Response(
        JSON.stringify({ prospects: cleanedProspects }),
        { status: 200, headers: { 'Content-Type': 'application/json' } }
      );

    } catch (parseError) {
      console.error('Failed to parse AI response:', parseError);
      return new Response(
        JSON.stringify({ error: 'Failed to parse extracted data' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } }
      );
    }

  } catch (error) {
    console.error('Error in AI prospect import:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}

function cleanWebsite(website) {
  if (!website) return '';
  let cleaned = website.toLowerCase().trim();
  if (!cleaned.startsWith('http')) {
    cleaned = 'https://' + cleaned;
  }
  return cleaned;
}

function cleanEmail(email) {
  if (!email) return '';
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email.trim()) ? email.trim().toLowerCase() : '';
}

function cleanPhone(phone) {
  if (!phone) return '';
  return phone.replace(/[^\d+\-\(\)\s]/g, '').trim();
}