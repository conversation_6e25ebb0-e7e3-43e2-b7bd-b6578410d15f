'use client';

import React from 'react';

const BGPattern = ({ 
  variant = 'dots', 
  mask = 'fade-edges', 
  size = 20, 
  fill = 'rgba(0,0,0,0.08)',
  className = ''
}) => {
  // Pattern generators
  const getPatternStyle = () => {
    switch (variant) {
      case 'dots':
        return {
          backgroundImage: `radial-gradient(circle at center, ${fill} 2px, transparent 2px)`,
          backgroundSize: `${size}px ${size}px`,
        };
      
      case 'grid':
        return {
          backgroundImage: `
            linear-gradient(${fill} 1px, transparent 1px),
            linear-gradient(90deg, ${fill} 1px, transparent 1px)
          `,
          backgroundSize: `${size}px ${size}px`,
        };
      
      case 'diagonal-stripes':
        return {
          backgroundImage: `repeating-linear-gradient(
            45deg,
            ${fill} 0px,
            ${fill} 1px,
            transparent 1px,
            transparent ${size}px
          )`,
        };
      
      default:
        return {};
    }
  };

  // Mask generators
  const getMaskStyle = () => {
    switch (mask) {
      case 'fade-edges':
        return {
          maskImage: 'radial-gradient(ellipse at center, white 40%, transparent 70%)',
          WebkitMaskImage: 'radial-gradient(ellipse at center, white 40%, transparent 70%)',
        };
      
      case 'fade-center':
        return {
          maskImage: 'radial-gradient(circle at center, transparent 20%, white 50%)',
          WebkitMaskImage: 'radial-gradient(circle at center, transparent 20%, white 50%)',
        };
      
      case 'fade-top':
        return {
          maskImage: 'linear-gradient(to bottom, transparent 0%, white 20%, white 100%)',
          WebkitMaskImage: 'linear-gradient(to bottom, transparent 0%, white 20%, white 100%)',
        };
      
      case 'fade-bottom':
        return {
          maskImage: 'linear-gradient(to top, transparent 0%, white 20%, white 100%)',
          WebkitMaskImage: 'linear-gradient(to top, transparent 0%, white 20%, white 100%)',
        };
      
      case 'fade-left':
        return {
          maskImage: 'linear-gradient(to right, transparent 0%, white 20%, white 100%)',
          WebkitMaskImage: 'linear-gradient(to right, transparent 0%, white 20%, white 100%)',
        };
      
      case 'fade-right':
        return {
          maskImage: 'linear-gradient(to left, transparent 0%, white 20%, white 100%)',
          WebkitMaskImage: 'linear-gradient(to left, transparent 0%, white 20%, white 100%)',
        };
      
      case 'none':
      default:
        return {};
    }
  };

  const combinedStyle = {
    ...getPatternStyle(),
    ...getMaskStyle(),
  };

  return (
    <div 
      className={`absolute inset-0 pointer-events-none z-[1] ${className}`}
      style={combinedStyle}
      aria-hidden="true"
    />
  );
};

export default BGPattern;