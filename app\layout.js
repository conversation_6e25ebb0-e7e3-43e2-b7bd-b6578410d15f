// app/layout.js
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import Header from "../components/header";
import Footer from "../components/Footer";
import NetworkStatus from "../components/NetworkStatus";
import cx from 'classnames';

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const viewport = {
  themeColor: "#ffffff",
  colorScheme: "light",
};

export const metadata = {
  title: "Inside Strategy Dashboard",
  description: "Manage your Inside Strategy operations.",
  manifest: "/manifest.json",
  appleWebApp: {
    capable: true,
    statusBarStyle: "black-translucent",
    title: "Inside Strategy Dashboard",
  },
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />
        <link rel="apple-touch-icon" href="/icons/icon-192x192.png" />
        <meta name="msapplication-config" content="/browserconfig.xml" />
        <meta name="msapplication-TileColor" content="#ffffff" />
        <script
          src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCwkqP67w9Hse_VUD78dsGx_D3cSZ-0Gac&libraries=places"
          async
          defer
        ></script>
      </head>
      <body className={cx(
        geistSans.variable,
        geistMono.variable,
        'flex flex-col min-h-screen bg-white text-[#2318f6] font-sans antialiased'
      )}>
        <NetworkStatus />
        <Header />
        
        {/* Main Content */}
        <main className="flex-1 w-full pt-20">
          {children}
        </main>
        
        <Footer />
      </body>
    </html>
  );
}
