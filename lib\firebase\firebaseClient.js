import { initializeApp, getApps } from "firebase/app";
import { getMessaging, getToken, onMessage } from "firebase/messaging";
import { getFirestore } from "firebase/firestore";
import { getAuth } from "firebase/auth";

const firebaseConfig = {
  apiKey: "AIzaSyDUry6QPaonpP0VzpB9YYXQy4G-PvrOZy0",
  authDomain: "inside-strategy.firebaseapp.com",
  databaseURL: "https://inside-strategy-default-rtdb.firebaseio.com",
  projectId: "inside-strategy",
  storageBucket: "inside-strategy.firebasestorage.app",
  messagingSenderId: "920642635898",
  appId: "1:920642635898:web:32f2bd5cb4abb62d5913c3",
  measurementId: "G-3Q5P57V3K0"
};

// Initialize Firebase only if it hasn't been initialized already
const app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApps()[0];
const messaging = typeof window !== "undefined" ? getMessaging(app) : null;
const db = getFirestore(app);
const auth = getAuth(app);

export { db, app, messaging, getToken, onMessage, auth };
