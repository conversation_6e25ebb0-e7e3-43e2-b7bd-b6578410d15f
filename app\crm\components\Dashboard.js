'use client';

import React, { useMemo } from 'react';

export default function Dashboard({ companies }) {
  const metrics = useMemo(() => {
    const total = companies.length;
    const statusCounts = companies.reduce((acc, company) => {
      acc[company.status] = (acc[company.status] || 0) + 1;
      return acc;
    }, {});

    const avgDataQuality = companies.length > 0 
      ? companies.reduce((sum, c) => sum + (c.data_quality_score || 0), 0) / companies.length
      : 0;

    const recentActivity = companies.filter(c => {
      const lastUpdate = new Date(c.last_updated);
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);
      return lastUpdate > weekAgo;
    }).length;

    return {
      total,
      statusCounts,
      avgDataQuality: Math.round(avgDataQuality * 10) / 10,
      recentActivity
    };
  }, [companies]);

  const StatusCard = ({ title, count, color }) => (
    <div className="bg-white p-6 rounded-lg shadow">
      <div className="flex items-center">
        <div className={`w-4 h-4 rounded-full ${color} mr-3`}></div>
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{count || 0}</p>
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-sm font-medium text-gray-600">Total Prospects</h3>
          <p className="text-3xl font-bold text-blue-600">{metrics.total}</p>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-sm font-medium text-gray-600">Avg Data Quality</h3>
          <p className="text-3xl font-bold text-green-600">{metrics.avgDataQuality}/10</p>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-sm font-medium text-gray-600">Recent Activity</h3>
          <p className="text-3xl font-bold text-purple-600">{metrics.recentActivity}</p>
          <p className="text-sm text-gray-500">Last 7 days</p>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-sm font-medium text-gray-600">Hot Leads</h3>
          <p className="text-3xl font-bold text-red-600">{metrics.statusCounts['Hot Lead'] || 0}</p>
        </div>
      </div>

      {/* Pipeline Status */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Pipeline Status</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
          <StatusCard title="Cold Lead" count={metrics.statusCounts['Cold Lead']} color="bg-gray-400" />
          <StatusCard title="Warm Lead" count={metrics.statusCounts['Warm Lead']} color="bg-yellow-400" />
          <StatusCard title="Hot Lead" count={metrics.statusCounts['Hot Lead']} color="bg-orange-400" />
          <StatusCard title="Meeting Set" count={metrics.statusCounts['Meeting Set']} color="bg-blue-400" />
          <StatusCard title="Under Contract" count={metrics.statusCounts['Under Contract']} color="bg-purple-400" />
          <StatusCard title="Closed Won" count={metrics.statusCounts['Closed Won']} color="bg-green-400" />
          <StatusCard title="Closed Lost" count={metrics.statusCounts['Closed Lost']} color="bg-red-400" />
        </div>
      </div>

      {/* Recent Companies */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Prospects</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Company
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Industry
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Data Quality
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Last Updated
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {companies.slice(0, 10).map((company) => (
                <tr key={company.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{company.company_name}</div>
                    <div className="text-sm text-gray-500">{company.dba_name}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {company.industry || 'N/A'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      company.status === 'Hot Lead' ? 'bg-red-100 text-red-800' :
                      company.status === 'Warm Lead' ? 'bg-yellow-100 text-yellow-800' :
                      company.status === 'Cold Lead' ? 'bg-gray-100 text-gray-800' :
                      company.status === 'Meeting Set' ? 'bg-blue-100 text-blue-800' :
                      company.status === 'Under Contract' ? 'bg-purple-100 text-purple-800' :
                      company.status === 'Closed Won' ? 'bg-green-100 text-green-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {company.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {company.data_quality_score || 0}/10
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {company.last_updated ? new Date(company.last_updated).toLocaleDateString() : 'N/A'}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}