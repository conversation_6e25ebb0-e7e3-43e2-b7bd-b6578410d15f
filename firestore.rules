rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read/write their own user document
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // SMS collections (existing)
    match /sms-kc/{document=**} {
      allow read, write: if request.auth != null;
    }
    
    match /sms-lwr/{document=**} {
      allow read, write: if request.auth != null;
    }
    
    // CRM Collections - Users can only access their own data
    match /companies/{companyId} {
      allow read, write: if request.auth != null && 
        (resource == null || resource.data.created_by == request.auth.uid);
      
      // Contacts subcollection
      match /contacts/{contactId} {
        allow read, write: if request.auth != null;
      }
      
      // Documents subcollection  
      match /documents/{documentId} {
        allow read, write: if request.auth != null;
      }
    }
    
    // Interactions - Users can only access their own
    match /interactions/{interactionId} {
      allow read, write: if request.auth != null && 
        (resource == null || resource.data.created_by == request.auth.uid);
    }
    
    // Follow-ups - Users can only access their own
    match /followups/{followupId} {
      allow read, write: if request.auth != null && 
        (resource == null || resource.data.created_by == request.auth.uid);
    }
    
    // System metrics - Users can only access their own
    match /system_metrics/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      
      match /monthly_stats/{document=**} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }
    }
  }
}