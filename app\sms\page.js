'use client';

import React, { useState, useEffect } from 'react';
import { onAuthStateChangedListener } from '../../auth';
import { doc, getDoc, collection, onSnapshot } from 'firebase/firestore';
import { db } from '../../lib/firebase/firebase';
import MessengerPopup from './sms-pop';
import ContactPopup from './contact-popup';
import BGPattern from '../../components/ui/BGPattern';

export default function MessengerPage() {
  const [contacts, setContacts] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [locationDetails, setLocationDetails] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [userDoc, setUserDoc] = useState(null);
  const [selectedContact, setSelectedContact] = useState(null);
  const [showConversation, setShowConversation] = useState(false);
  const [filterMode, setFilterMode] = useState('all');
  const [userName, setUserName] = useState('');
  const [showContactPopup, setShowContactPopup] = useState(false);
  const formatPhoneNumber = (phone) => {
    return phone || '';
  };

  useEffect(() => {
    const unsubscribe = onAuthStateChangedListener(async (user) => {
      if (user) {
        try {
          const docRef = doc(db, 'users', user.uid);
          const docSnap = await getDoc(docRef);
          if (docSnap.exists()) {
            const userData = docSnap.data();
            setUserDoc(userData);
            setUserName(user.displayName || '');

            // Directly set location details for KC branch with hardcoded business number
            setLocationDetails({
              businessNumber: '+***********', // Hardcoded for KC location
              collectionId: 'sms-kc',
              location: 'kc',
            });
          } else {
            throw new Error('User document not found');
          }
        } catch (error) {
          console.error('Error setting up messenger:', error);
          setUserDoc(null);
          setLocationDetails(null);
          setUserName('');
        }
      } else {
        setUserDoc(null);
        setLocationDetails(null);
        setUserName('');
      }
      setIsLoading(false);
    });

    return () => unsubscribe();
  }, []);

  useEffect(() => {
    if (!locationDetails?.collectionId) return;

    const smsCollection = collection(db, locationDetails.collectionId);
    const unsubscribe = onSnapshot(smsCollection, (snapshot) => {
      const contactsArr = [];
      snapshot.forEach((docSnap) => {
        const data = docSnap.data();
        const allMsgs = data.messages || [];

        allMsgs.sort((a, b) => {
          const aTime = (a.timestamp?.seconds || 0) * 1000;
          const bTime = (b.timestamp?.seconds || 0) * 1000;
          return bTime - aTime;
        });

        const lastMessage = allMsgs[0] || {};
        const lastMsgTimestamp = lastMessage.timestamp
          ? new Date(lastMessage.timestamp.seconds * 1000).toLocaleString()
          : null;

        const isUnread = allMsgs.some(msg => msg.direction === 'incoming' && !msg.read);

        contactsArr.push({
          id: docSnap.id,
          name: `${data.firstName || ''} ${data.lastName || ''}`.trim(),
          phone: docSnap.id,
          lastMessage: lastMessage.message || 'No messages yet',
          lastMsgTimestamp,
          lastMsgRawMillis: lastMessage.timestamp
            ? lastMessage.timestamp.seconds * 1000
            : 0,
          isUnread,
        });
      });

      contactsArr.sort((a, b) => b.lastMsgRawMillis - a.lastMsgRawMillis);
      setContacts(contactsArr);
    });

    return () => unsubscribe();
  }, [locationDetails]);

  const handleMarkAsRead = async (contact) => {
    console.log('Marking as read for contact:', contact.id);
    try {
      const response = await fetch(
        `https://inside-strategy-read-message-896343340170.europe-west1.run.app`,
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            collectionId: locationDetails.collectionId,
            contactId: contact.id,
          }),
        }
      );

      const result = await response.json();
      console.log('Mark as read response:', result);

      if (!response.ok) {
        throw new Error('Failed to mark message as read');
      }
    } catch (err) {
      console.error('Error marking as read:', err);
    }
  };

  const filteredContacts = contacts
    .filter((c) =>
      c.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      c.phone.includes(searchQuery)
    )
    .filter((c) => (filterMode === 'unread' ? c.isUnread : true));

  const handleSelectContact = (contact) => {
    console.log('Selected contact:', contact);
    setSelectedContact(contact);
    setShowConversation(true);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-gray-50 to-indigo-50 pt-3 pb-4 px-2 sm:px-4 sm:pt-20 font-sans tracking-tight relative overflow-hidden">
      {/* Background Pattern */}
      <BGPattern 
        variant="dots" 
        mask="fade-edges" 
        size={24} 
        fill="rgba(0,0,0,0.04)" 
      />
      
      {isLoading ? (
        <div className="flex justify-center items-center h-screen relative z-10">
          <div className="backdrop-blur-xl bg-white/20 rounded-2xl border border-white/30 shadow-2xl p-8">
            <p className="font-sans tracking-tight text-gray-700">Loading...</p>
          </div>
        </div>
      ) : !locationDetails ? (
        <div className="flex justify-center items-center h-screen relative z-10">
          <div className="backdrop-blur-xl bg-white/20 rounded-2xl border border-white/30 shadow-2xl p-8">
            <p className="font-sans tracking-tight text-gray-700">Contact admin to view this page</p>
          </div>
        </div>
      ) : (
        <div className="relative z-10">
          {/* Header Card */}
          {locationDetails && (
            <div className="mb-6 backdrop-blur-xl bg-white/30 rounded-lg border border-white/40 shadow-xl p-6">
              <h2 className="font-bold text-xl tracking-tight text-gray-800">{userName}</h2>
              <p className="text-sm tracking-tight text-gray-600">{locationDetails.location}</p>
            </div>
          )}

          {/* Filter Controls */}
          <div className="mb-6 backdrop-blur-xl bg-white/25 rounded-lg border border-white/30 shadow-lg p-4">
            <div className="flex flex-wrap gap-3 mb-4">
              <button
                onClick={() => setFilterMode('all')}
                className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                  filterMode === 'all'
                    ? 'bg-blue-500/80 text-white shadow-lg backdrop-blur-sm'
                    : 'bg-white/40 text-gray-700 hover:bg-white/60 backdrop-blur-sm'
                }`}
              >
                All ({contacts.length})
              </button>
              <button
                onClick={() => setFilterMode('unread')}
                className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                  filterMode === 'unread'
                    ? 'bg-red-500/80 text-white shadow-lg backdrop-blur-sm'
                    : 'bg-white/40 text-gray-700 hover:bg-white/60 backdrop-blur-sm'
                }`}
              >
                Unread ({contacts.filter(c => c.isUnread).length})
              </button>
              <button
                onClick={() => setShowContactPopup(true)}
                className="px-4 py-2 rounded-lg font-medium bg-green-500/80 text-white hover:bg-green-600/80 transition-all duration-200 shadow-lg backdrop-blur-sm"
              >
                New Message
              </button>
            </div>

            {/* Search Bar */}
            <input
              type="text"
              placeholder="Search contacts..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full px-4 py-3 rounded-lg backdrop-blur-sm bg-white/50 border border-white/40 placeholder-gray-500 text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-400/50 focus:bg-white/70 transition-all duration-200"
            />
          </div>

          {/* Contacts List */}
          <div className="space-y-3 overflow-y-auto font-sans tracking-tight">
            {filteredContacts.map((contact) => {
              const selectedClass =
                selectedContact?.id === contact.id
                  ? 'border-l-4 border-blue-500 bg-blue-500/20 backdrop-blur-xl'
                  : 'hover:bg-white/40 backdrop-blur-xl';

              return (
                <div
                  key={contact.id}
                  className={`relative p-5 rounded-lg cursor-pointer transition-all duration-300 ${selectedClass} bg-white/25 border border-white/30 shadow-lg hover:shadow-xl hover:scale-[1.02] font-sans tracking-tight`}
                  onClick={() => handleSelectContact(contact)}
                >
                  {contact.isUnread && (
                    <div className="absolute inset-0 bg-gradient-to-r from-red-400/20 to-orange-400/20 animate-pulse pointer-events-none rounded-lg" />
                  )}
                  <div className="relative z-10">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="font-semibold text-gray-800 tracking-tight text-lg">
                        {contact.name ? (
                          contact.name
                        ) : (
                          <span className="text-gray-600 bg-gray-200/60 border border-gray-300/50 px-3 py-1 rounded-md tracking-tight backdrop-blur-sm">
                            Unknown
                          </span>
                        )}
                      </h3>
                      {contact.isUnread && (
                        <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse shadow-lg"></div>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 tracking-tight mb-2 font-medium">{formatPhoneNumber(contact.phone)}</p>
                    <p className="text-sm text-gray-700 mb-2 font-medium tracking-tight line-clamp-2">
                      {contact.lastMessage}
                    </p>
                    <div className="flex justify-between items-center">
                      {contact.lastMsgTimestamp && (
                        <p className="text-xs text-gray-500 tracking-tight">
                          {contact.lastMsgTimestamp}
                        </p>
                      )}
                      {contact.isUnread && (
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleMarkAsRead(contact);
                          }}
                          className="px-4 py-2 bg-blue-500/80 text-white rounded-lg font-medium tracking-tight hover:bg-blue-600/80 transition-all duration-200 shadow-lg backdrop-blur-sm"
                        >
                          Mark Read
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Popups remain the same */}
          {showConversation && selectedContact && (
            <MessengerPopup
              isOpen={showConversation}
              onClose={() => {
                setShowConversation(false);
                setSelectedContact(null);
              }}
              businessNumber={locationDetails?.businessNumber || ''}
              clientNumber={selectedContact.phone}
              location={locationDetails?.location || ''}
            />
          )}

          {showContactPopup && (
            <ContactPopup
              isOpen={showContactPopup}
              onClose={() => setShowContactPopup(false)}
              businessNumber={locationDetails?.businessNumber || ''}
            />
          )}
        </div>
      )}
    </div>
  );
}
