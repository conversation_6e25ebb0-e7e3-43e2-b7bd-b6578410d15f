'use client';

import React, { useState } from 'react';
import { db, auth } from '../../../lib/firebase/firebaseClient';
import { collection, addDoc } from 'firebase/firestore';
import { useAuthState } from 'react-firebase-hooks/auth';

const INTERACTION_TYPES = ['Phone Call', 'Email', 'In-Person Meeting', 'Text Message', 'Review Left', 'Voicemail'];
const WHO_ANSWERED = ['Target Owner', 'Co-Owner', 'Secretary', 'Employee', 'Voicemail', 'No Answer'];
const SCRIPTS = ['Acquisition Script', 'Direct Selling Script', 'Hybrid Script', 'Follow-up Script'];
const RESPONSES = ['Interested', 'Neutral', 'Resistant', 'Hostile', 'No Response'];
const YES_NO_MAYBE = ['Yes', 'No', 'Maybe', 'Not Asked'];
const TIMELINES = ['Now', '6 months', '1 year', '2-3 years', '5+ years', 'Never'];

export default function InteractionForm({ company, onClose, onSave }) {
  const [formData, setFormData] = useState({
    interaction_type: 'Phone Call',
    interaction_date: new Date().toISOString().slice(0, 16),
    duration_minutes: '',
    who_answered: '',
    script_used: '',
    initial_response: '',
    interest_in_acquiring: 'Not Asked',
    acquiring_resistance_reason: '',
    interest_in_selling: 'Not Asked',
    selling_timeline: '',
    selling_reasons: [],
    valuation_expectations: '',
    family_succession_plans: 'Not Discussed',
    other_brokers_contacted: false,
    previously_listed: false,
    other_buyers_circling: false,
    interaction_notes: '',
    next_action_required: '',
    follow_up_date: '',
    outcome_rating: 5
  });

  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const interactionData = {
        ...formData,
        company_id: company.id,
        company_name: company.company_name,
        interaction_date: new Date(formData.interaction_date).toISOString(),
        follow_up_date: formData.follow_up_date ? new Date(formData.follow_up_date).toISOString() : null,
        created_date: new Date().toISOString()
      };

      await addDoc(collection(db, 'interactions'), interactionData);

      // Create follow-up if needed
      if (formData.follow_up_date && formData.next_action_required) {
        await addDoc(collection(db, 'followups'), {
          company_id: company.id,
          company_name: company.company_name,
          scheduled_date: new Date(formData.follow_up_date).toISOString(),
          followup_type: formData.interaction_type,
          description: formData.next_action_required,
          priority: 'Medium',
          completed: false,
          created_date: new Date().toISOString()
        });
      }

      onSave();
    } catch (error) {
      console.error('Error saving interaction:', error);
      alert('Error saving interaction. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSellingReasonsChange = (reason) => {
    setFormData(prev => ({
      ...prev,
      selling_reasons: prev.selling_reasons.includes(reason)
        ? prev.selling_reasons.filter(r => r !== reason)
        : [...prev.selling_reasons, reason]
    }));
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-gray-900">
              Log Interaction - {company.company_name}
            </h2>
            <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Interaction Info */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Interaction Type
                </label>
                <select
                  name="interaction_type"
                  value={formData.interaction_type}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  required
                >
                  {INTERACTION_TYPES.map(type => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Date & Time
                </label>
                <input
                  type="datetime-local"
                  name="interaction_date"
                  value={formData.interaction_date}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Duration (minutes)
                </label>
                <input
                  type="number"
                  name="duration_minutes"
                  value={formData.duration_minutes}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                />
              </div>
            </div>

            {/* Call Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Who Answered
                </label>
                <select
                  name="who_answered"
                  value={formData.who_answered}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                >
                  <option value="">Select...</option>
                  {WHO_ANSWERED.map(who => (
                    <option key={who} value={who}>{who}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Script Used
                </label>
                <select
                  name="script_used"
                  value={formData.script_used}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                >
                  <option value="">Select...</option>
                  {SCRIPTS.map(script => (
                    <option key={script} value={script}>{script}</option>
                  ))}
                </select>
              </div>
            </div>

            {/* Interest Assessment */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Interest in Selling
                </label>
                <select
                  name="interest_in_selling"
                  value={formData.interest_in_selling}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                >
                  {YES_NO_MAYBE.map(option => (
                    <option key={option} value={option}>{option}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Selling Timeline
                </label>
                <select
                  name="selling_timeline"
                  value={formData.selling_timeline}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                >
                  <option value="">Select...</option>
                  {TIMELINES.map(timeline => (
                    <option key={timeline} value={timeline}>{timeline}</option>
                  ))}
                </select>
              </div>
            </div>

            {/* Notes */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Interaction Notes
              </label>
              <textarea
                name="interaction_notes"
                value={formData.interaction_notes}
                onChange={handleChange}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                placeholder="Detailed notes about the conversation..."
              />
            </div>

            {/* Follow-up */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Next Action Required
                </label>
                <input
                  type="text"
                  name="next_action_required"
                  value={formData.next_action_required}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  placeholder="e.g., Send information packet"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Follow-up Date
                </label>
                <input
                  type="date"
                  name="follow_up_date"
                  value={formData.follow_up_date}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                />
              </div>
            </div>

            {/* Outcome Rating */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Outcome Rating (1-10)
              </label>
              <input
                type="range"
                name="outcome_rating"
                min="1"
                max="10"
                value={formData.outcome_rating}
                onChange={handleChange}
                className="w-full"
              />
              <div className="text-center text-sm text-gray-600">{formData.outcome_rating}/10</div>
            </div>

            {/* Form Actions */}
            <div className="flex justify-end space-x-4 pt-6 border-t">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                {loading ? 'Saving...' : 'Save Interaction'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
