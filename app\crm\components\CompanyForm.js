'use client';

import React, { useState, useEffect } from 'react';
import { db, auth } from '../../../lib/firebase/firebaseClient';
import { collection, addDoc, updateDoc, doc } from 'firebase/firestore';
import { useAuthState } from 'react-firebase-hooks/auth';

const BUSINESS_TYPES = ['LLC', 'Corporation', 'Partnership', 'Sole Proprietorship'];
const INDUSTRIES = ['HVAC', 'Plumbing', 'Manufacturing', 'Construction', 'Other'];
const LEGAL_STATUSES = ['Active', 'Inactive', 'Dissolved', 'Forfeited'];
const GROWTH_STATUSES = ['Growing', 'Stable', 'Declining', 'Unknown'];
const COMPETITION_LEVELS = ['High', 'Medium', 'Low'];
const DATA_SOURCES = ['State Registry', 'Commercial Database', 'Referral'];
const STATUSES = ['Cold Lead', 'Warm Lead', 'Hot Lead', 'Meeting Set', 'Under Contract', 'Closed Won', 'Closed Lost'];

export default function CompanyForm({ company, onClose, onSave }) {
  const [formData, setFormData] = useState({
    company_name: '',
    dba_name: '',
    entity_id: '',
    formation_date: '',
    business_type: '',
    industry: '',
    state: '',
    legal_status: 'Active',
    business_address: '',
    business_city: '',
    business_state: '',
    business_zip: '',
    phone_primary: '',
    phone_verified: false,
    website_url: '',
    website_active: null,
    email_general: '',
    email_verified: false,
    estimated_revenue_min: '',
    estimated_revenue_max: '',
    employee_count: '',
    growth_status: 'Unknown',
    reputation_score: 5,
    competition_level: 'Medium',
    assets_owned: '',
    specializations: '',
    data_source_primary: 'State Registry',
    data_quality_score: 5,
    research_time_minutes: '',
    research_cost_dollars: '',
    status: 'Cold Lead'
  });

  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  useEffect(() => {
    if (company) {
      // Ensure all fields have values to prevent controlled/uncontrolled input warnings
      setFormData({
        company_name: company.company_name || '',
        dba_name: company.dba_name || '',
        entity_id: company.entity_id || '',
        formation_date: company.formation_date || '',
        business_type: company.business_type || '',
        industry: company.industry || '',
        state: company.state || '',
        legal_status: company.legal_status || 'Active',
        business_address: company.business_address || '',
        business_city: company.business_city || '',
        business_state: company.business_state || '',
        business_zip: company.business_zip || '',
        phone_primary: company.phone_primary || '',
        phone_verified: company.phone_verified || false,
        website_url: company.website_url || '',
        website_active: company.website_active || null,
        email_general: company.email_general || '',
        email_verified: company.email_verified || false,
        estimated_revenue_min: company.estimated_revenue_min || '',
        estimated_revenue_max: company.estimated_revenue_max || '',
        employee_count: company.employee_count || '',
        growth_status: company.growth_status || 'Unknown',
        reputation_score: company.reputation_score || 5,
        competition_level: company.competition_level || 'Medium',
        assets_owned: company.assets_owned || '',
        specializations: company.specializations || '',
        data_source_primary: company.data_source_primary || 'State Registry',
        data_quality_score: company.data_quality_score || 5,
        research_time_minutes: company.research_time_minutes || '',
        research_cost_dollars: company.research_cost_dollars || '',
        status: company.status || 'Cold Lead'
      });
    }
  }, [company]);

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.company_name.trim()) {
      newErrors.company_name = 'Company name is required';
    }
    
    if (formData.phone_primary && !/^\d{3}-\d{3}-\d{4}$/.test(formData.phone_primary)) {
      newErrors.phone_primary = 'Phone format should be XXX-XXX-XXXX';
    }
    
    if (formData.email_general && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email_general)) {
      newErrors.email_general = 'Invalid email format';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const calculateDataQuality = () => {
    const fields = [
      'company_name', 'business_type', 'industry', 'state', 'business_address',
      'phone_primary', 'email_general', 'estimated_revenue_min', 'employee_count'
    ];
    
    const filledFields = fields.filter(field => formData[field] && formData[field].toString().trim());
    return Math.round((filledFields.length / fields.length) * 10);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) return;
    
    setLoading(true);
    
    try {
      const dataToSave = {
        ...formData,
        data_quality_score: calculateDataQuality(),
        business_age_years: formData.formation_date ? 
          new Date().getFullYear() - new Date(formData.formation_date).getFullYear() : null,
        last_updated: new Date().toISOString(),
        ...(company ? {} : { created_date: new Date().toISOString() })
      };

      if (company) {
        await updateDoc(doc(db, 'companies', company.id), dataToSave);
      } else {
        await addDoc(collection(db, 'companies'), dataToSave);
      }
      
      onSave();
    } catch (error) {
      console.error('Error saving company:', error);
      alert('Error saving company. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-gray-900">
              {company ? 'Edit Company' : 'Add New Company'}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Company Name *
                </label>
                <input
                  type="text"
                  name="company_name"
                  value={formData.company_name}
                  onChange={handleChange}
                  className={`w-full px-3 py-2 border rounded-md ${errors.company_name ? 'border-red-500' : 'border-gray-300'}`}
                  required
                />
                {errors.company_name && <p className="text-red-500 text-sm mt-1">{errors.company_name}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  DBA Name
                </label>
                <input
                  type="text"
                  name="dba_name"
                  value={formData.dba_name}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Business Type
                </label>
                <select
                  name="business_type"
                  value={formData.business_type}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                >
                  <option value="">Select Type</option>
                  {BUSINESS_TYPES.map(type => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Industry
                </label>
                <select
                  name="industry"
                  value={formData.industry}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                >
                  <option value="">Select Industry</option>
                  {INDUSTRIES.map(industry => (
                    <option key={industry} value={industry}>{industry}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Phone Number
                </label>
                <input
                  type="text"
                  name="phone_primary"
                  value={formData.phone_primary}
                  onChange={handleChange}
                  placeholder="XXX-XXX-XXXX"
                  className={`w-full px-3 py-2 border rounded-md ${errors.phone_primary ? 'border-red-500' : 'border-gray-300'}`}
                />
                {errors.phone_primary && <p className="text-red-500 text-sm mt-1">{errors.phone_primary}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email
                </label>
                <input
                  type="email"
                  name="email_general"
                  value={formData.email_general}
                  onChange={handleChange}
                  className={`w-full px-3 py-2 border rounded-md ${errors.email_general ? 'border-red-500' : 'border-gray-300'}`}
                />
                {errors.email_general && <p className="text-red-500 text-sm mt-1">{errors.email_general}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Status
                </label>
                <select
                  name="status"
                  value={formData.status}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                >
                  {STATUSES.map(status => (
                    <option key={status} value={status}>{status}</option>
                  ))}
                </select>
              </div>
            </div>

            {/* Form Actions */}
            <div className="flex justify-end space-x-4 pt-6 border-t">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                {loading ? 'Saving...' : (company ? 'Update' : 'Create')}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
