'use client';

import React, { useState, useEffect } from 'react';
import { db } from '../../../lib/firebase/firebaseClient';
import { collection, addDoc, updateDoc, deleteDoc, doc, onSnapshot, query, orderBy } from 'firebase/firestore';

export default function ContactsManager({ company, onClose }) {
  const [contacts, setContacts] = useState([]);
  const [showForm, setShowForm] = useState(false);
  const [editingContact, setEditingContact] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    title: '',
    email: '',
    phone: '',
    linkedin: '',
    notes: '',
    is_primary: false
  });

  useEffect(() => {
    if (!company?.id) return;

    const contactsRef = collection(db, 'companies', company.id, 'contacts');
    const q = query(contactsRef, orderBy('created_date', 'desc'));
    
    const unsubscribe = onSnapshot(q, (snapshot) => {
      const contactsData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      setContacts(contactsData);
    });

    return () => unsubscribe();
  }, [company?.id]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      const contactData = {
        ...formData,
        last_updated: new Date().toISOString()
      };

      if (editingContact) {
        await updateDoc(doc(db, 'companies', company.id, 'contacts', editingContact.id), contactData);
      } else {
        contactData.created_date = new Date().toISOString();
        await addDoc(collection(db, 'companies', company.id, 'contacts'), contactData);
      }

      // If this is set as primary, remove primary from others
      if (formData.is_primary) {
        const otherContacts = contacts.filter(c => c.id !== editingContact?.id);
        for (const contact of otherContacts) {
          if (contact.is_primary) {
            await updateDoc(doc(db, 'companies', company.id, 'contacts', contact.id), {
              is_primary: false
            });
          }
        }
      }

      resetForm();
    } catch (error) {
      console.error('Error saving contact:', error);
      alert('Error saving contact');
    }
  };

  const handleDelete = async (contactId) => {
    if (!confirm('Delete this contact?')) return;
    
    try {
      await deleteDoc(doc(db, 'companies', company.id, 'contacts', contactId));
    } catch (error) {
      console.error('Error deleting contact:', error);
      alert('Error deleting contact');
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      title: '',
      email: '',
      phone: '',
      linkedin: '',
      notes: '',
      is_primary: false
    });
    setEditingContact(null);
    setShowForm(false);
  };

  const startEdit = (contact) => {
    setFormData(contact);
    setEditingContact(contact);
    setShowForm(true);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-bold">Contacts - {company.company_name}</h2>
            <button onClick={onClose} className="text-gray-500 hover:text-gray-700">✕</button>
          </div>
        </div>

        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h3 className="text-lg font-medium">All Contacts ({contacts.length})</h3>
            <button
              onClick={() => setShowForm(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
            >
              Add Contact
            </button>
          </div>

          {/* Contacts List */}
          <div className="space-y-4">
            {contacts.map((contact) => (
              <div key={contact.id} className="border rounded-lg p-4 bg-gray-50">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium">{contact.name}</h4>
                      {contact.is_primary && (
                        <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                          Primary
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-gray-600">{contact.title}</p>
                    <div className="mt-2 space-y-1">
                      {contact.email && (
                        <p className="text-sm">
                          <strong>Email:</strong> 
                          <a href={`mailto:${contact.email}`} className="text-blue-600 ml-1">
                            {contact.email}
                          </a>
                        </p>
                      )}
                      {contact.phone && (
                        <p className="text-sm">
                          <strong>Phone:</strong> 
                          <a href={`tel:${contact.phone}`} className="text-blue-600 ml-1">
                            {contact.phone}
                          </a>
                        </p>
                      )}
                      {contact.linkedin && (
                        <p className="text-sm">
                          <strong>LinkedIn:</strong> 
                          <a href={contact.linkedin} target="_blank" rel="noopener noreferrer" className="text-blue-600 ml-1">
                            View Profile
                          </a>
                        </p>
                      )}
                      {contact.notes && (
                        <p className="text-sm"><strong>Notes:</strong> {contact.notes}</p>
                      )}
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <button
                      onClick={() => startEdit(contact)}
                      className="text-blue-600 hover:text-blue-800 text-sm"
                    >
                      Edit
                    </button>
                    <button
                      onClick={() => handleDelete(contact.id)}
                      className="text-red-600 hover:text-red-800 text-sm"
                    >
                      Delete
                    </button>
                  </div>
                </div>
              </div>
            ))}
            
            {contacts.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                No contacts yet. Add the first contact to get started.
              </div>
            )}
          </div>

          {/* Contact Form Modal */}
          {showForm && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60">
              <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
                <div className="p-6 border-b">
                  <h3 className="text-lg font-bold">
                    {editingContact ? 'Edit Contact' : 'Add Contact'}
                  </h3>
                </div>
                
                <form onSubmit={handleSubmit} className="p-6 space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">Name *</label>
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) => setFormData({...formData, name: e.target.value})}
                      className="w-full p-2 border rounded-lg"
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-1">Title</label>
                    <input
                      type="text"
                      value={formData.title}
                      onChange={(e) => setFormData({...formData, title: e.target.value})}
                      className="w-full p-2 border rounded-lg"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-1">Email</label>
                    <input
                      type="email"
                      value={formData.email}
                      onChange={(e) => setFormData({...formData, email: e.target.value})}
                      className="w-full p-2 border rounded-lg"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-1">Phone</label>
                    <input
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => setFormData({...formData, phone: e.target.value})}
                      className="w-full p-2 border rounded-lg"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-1">LinkedIn</label>
                    <input
                      type="url"
                      value={formData.linkedin}
                      onChange={(e) => setFormData({...formData, linkedin: e.target.value})}
                      className="w-full p-2 border rounded-lg"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-1">Notes</label>
                    <textarea
                      value={formData.notes}
                      onChange={(e) => setFormData({...formData, notes: e.target.value})}
                      className="w-full p-2 border rounded-lg h-20"
                    />
                  </div>
                  
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.is_primary}
                      onChange={(e) => setFormData({...formData, is_primary: e.target.checked})}
                      className="mr-2"
                    />
                    <label className="text-sm">Primary Contact</label>
                  </div>
                  
                  <div className="flex gap-3 pt-4">
                    <button
                      type="submit"
                      className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
                    >
                      {editingContact ? 'Update' : 'Add'} Contact
                    </button>
                    <button
                      type="button"
                      onClick={resetForm}
                      className="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600"
                    >
                      Cancel
                    </button>
                  </div>
                </form>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}