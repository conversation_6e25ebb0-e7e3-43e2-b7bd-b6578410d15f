'use client';

import React, { useState } from 'react';
import ContactsManager from './ContactsManager';
import { db } from '../../../lib/firebase/firebaseClient';
import { doc, deleteDoc, collection, getDocs } from 'firebase/firestore';

export default function CompanyList({ companies, onEdit, onAddInteraction, onDelete }) {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedCompany, setSelectedCompany] = useState(null);
  const [showContacts, setShowContacts] = useState(false);

  const filteredCompanies = companies.filter(company => {
    const matchesSearch = company.company_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         company.industry?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || company.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const handleManageContacts = (company) => {
    setSelectedCompany(company);
    setShowContacts(true);
  };

  const handleDelete = async (company) => {
    if (!confirm(`Are you sure you want to delete "${company.company_name}"? This action cannot be undone.`)) {
      return;
    }

    try {
      // Delete all contacts first
      const contactsRef = collection(db, 'companies', company.id, 'contacts');
      const contactsSnapshot = await getDocs(contactsRef);

      for (const contactDoc of contactsSnapshot.docs) {
        await deleteDoc(doc(db, 'companies', company.id, 'contacts', contactDoc.id));
      }

      // Delete the company
      await deleteDoc(doc(db, 'companies', company.id));

      // Call the parent's onDelete if provided
      if (onDelete) {
        onDelete(company.id);
      }
    } catch (error) {
      console.error('Error deleting company:', error);
      alert('Error deleting company. Please try again.');
    }
  };

  return (
    <div className="space-y-6">
      {/* Filters */}
      <div className="flex gap-4 items-center">
        <input
          type="text"
          placeholder="Search companies..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="flex-1 p-2 border rounded-lg"
        />
        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className="p-2 border rounded-lg"
        >
          <option value="all">All Status</option>
          <option value="New Lead">New Lead</option>
          <option value="Contacted">Contacted</option>
          <option value="Qualified">Qualified</option>
          <option value="Proposal">Proposal</option>
          <option value="Negotiation">Negotiation</option>
          <option value="Closed Won">Closed Won</option>
          <option value="Closed Lost">Closed Lost</option>
        </select>
      </div>

      {/* Companies Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {filteredCompanies.map((company) => (
          <div key={company.id} className="bg-white rounded-lg shadow-md p-6 border">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">{company.company_name}</h3>
                <p className="text-sm text-gray-600">{company.industry}</p>
              </div>
              <span className={`px-2 py-1 text-xs rounded-full ${
                company.status === 'New Lead' ? 'bg-blue-100 text-blue-800' :
                company.status === 'Qualified' ? 'bg-green-100 text-green-800' :
                company.status === 'Closed Won' ? 'bg-emerald-100 text-emerald-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                {company.status}
              </span>
            </div>

            <div className="space-y-2 text-sm text-gray-600 mb-4">
              {company.website && (
                <p>🌐 <a href={company.website} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                  {company.website}
                </a></p>
              )}
              {company.revenue_range && <p>💰 {company.revenue_range}</p>}
              {company.employee_count && <p>👥 {company.employee_count} employees</p>}
              <p>📊 Data Quality: {company.data_quality_score || 0}%</p>
            </div>

            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => onEdit(company)}
                className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700"
              >
                Edit
              </button>
              <button
                onClick={() => handleManageContacts(company)}
                className="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700"
              >
                Contacts
              </button>
              <button
                onClick={() => onAddInteraction(company)}
                className="bg-purple-600 text-white px-3 py-1 rounded text-sm hover:bg-purple-700"
              >
                Log Call
              </button>
              <button
                onClick={() => handleDelete(company)}
                className="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700"
              >
                Delete
              </button>
            </div>
          </div>
        ))}
      </div>

      {filteredCompanies.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500">No companies found matching your criteria.</p>
        </div>
      )}

      {/* Contacts Manager Modal */}
      {showContacts && selectedCompany && (
        <ContactsManager
          company={selectedCompany}
          onClose={() => {
            setShowContacts(false);
            setSelectedCompany(null);
          }}
        />
      )}
    </div>
  );
}
