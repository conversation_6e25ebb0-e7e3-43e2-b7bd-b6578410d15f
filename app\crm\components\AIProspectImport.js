'use client';

import React, { useState } from 'react';
import { db, auth } from '../../../lib/firebase/firebaseClient';
import { collection, addDoc, updateDoc, doc, query, where, getDocs } from 'firebase/firestore';
import { useAuthState } from 'react-firebase-hooks/auth';

export default function AIProspectImport({ onClose, onImportComplete }) {
  const [user] = useAuthState(auth);
  const [inputText, setInputText] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [results, setResults] = useState(null);
  const [previewData, setPreviewData] = useState([]);

  const processWithAI = async () => {
    if (!inputText.trim()) return;
    
    setIsProcessing(true);
    try {
      const response = await fetch('/api/ai-prospect-import', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ text: inputText.trim() })
      });

      if (!response.ok) throw new Error('Failed to process data');
      
      const data = await response.json();
      setPreviewData(data.prospects || []);
      setResults(data);
    } catch (error) {
      console.error('Error processing with AI:', error);
      alert('Error processing data. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const importProspects = async () => {
    if (!previewData.length || !user) return;
    
    setIsProcessing(true);
    try {
      let imported = 0;
      let updated = 0;

      for (const prospect of previewData) {
        // Check if company already exists
        const companiesRef = collection(db, 'companies');
        const q = query(
          companiesRef, 
          where('company_name', '==', prospect.company_name),
          where('created_by', '==', user.uid)
        );
        const existingDocs = await getDocs(q);

        const companyData = {
          ...prospect,
          created_by: user.uid,
          last_updated: new Date().toISOString(),
          data_quality_score: calculateDataQuality(prospect)
        };

        if (existingDocs.empty) {
          // Create new company
          companyData.created_date = new Date().toISOString();
          const docRef = await addDoc(companiesRef, companyData);
          
          // Add contacts as subcollection
          if (prospect.contacts?.length) {
            for (const contact of prospect.contacts) {
              await addDoc(collection(db, 'companies', docRef.id, 'contacts'), {
                ...contact,
                created_date: new Date().toISOString()
              });
            }
          }
          imported++;
        } else {
          // Update existing company
          const existingDoc = existingDocs.docs[0];
          await updateDoc(doc(db, 'companies', existingDoc.id), companyData);
          
          // Add new contacts
          if (prospect.contacts?.length) {
            for (const contact of prospect.contacts) {
              await addDoc(collection(db, 'companies', existingDoc.id, 'contacts'), {
                ...contact,
                created_date: new Date().toISOString()
              });
            }
          }
          updated++;
        }
      }

      alert(`Import complete! ${imported} new companies created, ${updated} companies updated.`);
      onImportComplete();
      onClose();
    } catch (error) {
      console.error('Error importing prospects:', error);
      alert('Error importing prospects. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const calculateDataQuality = (prospect) => {
    let score = 0;
    if (prospect.company_name) score += 20;
    if (prospect.industry) score += 15;
    if (prospect.website) score += 15;
    if (prospect.address) score += 10;
    if (prospect.contacts?.length) score += 25;
    if (prospect.revenue_range) score += 15;
    return Math.min(score, 100);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-bold">AI Prospect Import</h2>
            <button onClick={onClose} className="text-gray-500 hover:text-gray-700">✕</button>
          </div>
        </div>

        <div className="p-6 space-y-6">
          {!results ? (
            <>
              <div>
                <label className="block text-sm font-medium mb-2">
                  Paste prospect data (any format - text, CSV, emails, etc.)
                </label>
                <textarea
                  value={inputText}
                  onChange={(e) => setInputText(e.target.value)}
                  className="w-full h-64 p-3 border rounded-lg resize-none"
                  placeholder="Paste your prospect data here... AI will automatically extract company names, contacts, emails, phones, addresses, etc."
                />
              </div>
              
              <div className="flex gap-3">
                <button
                  onClick={processWithAI}
                  disabled={isProcessing || !inputText.trim()}
                  className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50"
                >
                  {isProcessing ? 'Processing with AI...' : 'Process with AI'}
                </button>
              </div>
            </>
          ) : (
            <>
              <div className="bg-green-50 p-4 rounded-lg">
                <h3 className="font-medium text-green-800 mb-2">
                  Found {previewData.length} prospects
                </h3>
                <p className="text-sm text-green-600">
                  Review the data below and click Import to add to your CRM
                </p>
              </div>

              <div className="space-y-4 max-h-96 overflow-y-auto">
                {previewData.map((prospect, index) => (
                  <div key={index} className="border rounded-lg p-4 bg-gray-50">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <h4 className="font-medium">{prospect.company_name}</h4>
                        <p className="text-sm text-gray-600">{prospect.industry}</p>
                        <p className="text-sm text-gray-600">{prospect.website}</p>
                      </div>
                      <div>
                        <p className="text-sm"><strong>Contacts:</strong> {prospect.contacts?.length || 0}</p>
                        <p className="text-sm"><strong>Revenue:</strong> {prospect.revenue_range}</p>
                        <p className="text-sm"><strong>Employees:</strong> {prospect.employee_count}</p>
                      </div>
                    </div>
                    
                    {prospect.contacts?.length > 0 && (
                      <div className="mt-3 pt-3 border-t">
                        <p className="text-sm font-medium mb-2">Contacts:</p>
                        <div className="grid grid-cols-2 gap-2">
                          {prospect.contacts.map((contact, idx) => (
                            <div key={idx} className="text-sm bg-white p-2 rounded">
                              <p><strong>{contact.name}</strong> - {contact.title}</p>
                              <p>{contact.email}</p>
                              <p>{contact.phone}</p>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>

              <div className="flex gap-3">
                <button
                  onClick={importProspects}
                  disabled={isProcessing}
                  className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 disabled:opacity-50"
                >
                  {isProcessing ? 'Importing...' : `Import ${previewData.length} Prospects`}
                </button>
                <button
                  onClick={() => {
                    setResults(null);
                    setPreviewData([]);
                  }}
                  className="bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600"
                >
                  Start Over
                </button>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
}