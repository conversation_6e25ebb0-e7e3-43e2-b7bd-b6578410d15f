@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  @property --pos-x {
    syntax: '<percentage>';
    initial-value: 11.14%;
    inherits: false;
  }

  @property --pos-y {
    syntax: '<percentage>';
    initial-value: 140%;
    inherits: false;
  }

  @property --spread-x {
    syntax: '<percentage>';
    initial-value: 150%;
    inherits: false;
  }

  @property --spread-y {
    syntax: '<percentage>';
    initial-value: 180.06%;
    inherits: false;
  }

  @property --color-1 {
    syntax: '<color>';
    initial-value: #1e3a8a;
    inherits: false;
  }

  @property --color-2 {
    syntax: '<color>';
    initial-value: #3b82f6;
    inherits: false;
  }

  @property --color-3 {
    syntax: '<color>';
    initial-value: #1d4ed8;
    inherits: false;
  }

  @property --color-4 {
    syntax: '<color>';
    initial-value: #2563eb;
    inherits: false;
  }
}

@layer components {
  .gradient-button {
    @apply relative appearance-none cursor-pointer;
    background: radial-gradient(var(--spread-x) var(--spread-y) at var(--pos-x) var(--pos-y),
        var(--color-1) var(--stop-1),
        var(--color-2) var(--stop-2),
        var(--color-3) var(--stop-3),
        var(--color-4) var(--stop-4),
        var(--color-5) var(--stop-5));
    transition:
      --pos-x 0.5s,
      --pos-y 0.5s,
      --spread-x 0.5s,
      --spread-y 0.5s,
      --color-1 0.5s,
      --color-2 0.5s,
      --color-3 0.5s,
      --color-4 0.5s,
      --color-5 0.5s,
      --border-angle 0.5s,
      --border-color-1 0.5s,
      --border-color-2 0.5s,
      --stop-1 0.5s,
      --stop-2 0.5s,
      --stop-3 0.5s,
      --stop-4 0.5s,
      --stop-5 0.5s;
  }

  .gradient-button:active {
    transform: translateY(0px) scale(0.98);
    animation:
      gradientFlow 1s ease-in-out infinite,
      colorShift 1.5s ease-in-out infinite,
      borderRotate 0.8s linear infinite;
  }

  .gradient-button-variant {
    --color-1: #1e40af;
    --color-2: #3b82f6;
    --color-3: #60a5fa;
    --color-4: #93c5fd;
    --border-angle: 200deg;
    --border-color-1: hsla(220, 75%, 50%, 0.6);
    --border-color-2: hsla(220, 85%, 30%, 0.8);

    animation:
      gradientFlow 10s ease-in-out infinite reverse,
      colorShift 15s ease-in-out infinite,
      borderRotate 12s linear infinite reverse;
  }
}

body {
  color: #2318f6;
  background: white;
  font-family: system-ui, -apple-system, sans-serif;
  margin: 0;
  min-height: 100vh;
}

* {
  transition: all 0.3s ease-in-out;
  box-sizing: border-box;
}

button,
a {
  position: relative;
  -webkit-tap-highlight-color: transparent;
}

/* Calendar styles */
.calendar-container {
  max-width: 100%;
  margin: 0 auto;
  padding: 0 8px;
}

.calendar-day {
  transition: background-color 0.3s ease-in-out;
  text-align: center;
  padding: 4px;
  border: 1px solid #e5e7eb;
}

.calendar-day:hover {
  background-color: #f3f4f6;
}

.calendar-event {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 2px 4px;
  font-size: 12px;
  margin: 2px 0;
  background-color: #2563eb;
  color: white;
  border-radius: 4px;
  cursor: pointer;
  display: block;
}

.calendar-event:hover {
  background-color: #1e3a8a;
}

@media (max-width: 640px) {
  .calendar-container {
    padding: 0;
    max-width: 100%;
    margin: 0;
  }

  .calendar-button {
    font-size: 10px;
    padding: 2px;
  }

  .calendar-day {
    padding: 2px;
    font-size: 12px;
  }

  .calendar-event {
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 10px;
    padding: 1px 2px;
    margin-bottom: 2px;
    background-color: #2563eb;
    color: white;
    border-radius: 4px;
    display: inline-block;
  }

  .calendar-day:hover .calendar-event {
    background-color: #1e3a8a;
  }
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.75);
  z-index: 1000;
}

.modal {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  padding: 1rem;
  border-radius: 0.5rem;
  max-width: 90%;
  max-height: 90vh;
  width: auto;
  overflow: auto;
  outline: none;
}

.modal img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 0 auto;
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
